{"@@locale": "en", "appName": "Anx <PERSON>", "app_about": "About Anx <PERSON>", "app_version": "Version", "app_license": "License", "app_author": "Author", "app_donate": "Donate", "app_donate_tips": "Anx Reader is an open-source software that could free to use. If you find it helpful, please donate to support us to continue improving it.", "navBar_bookshelf": "Bookshelf", "navBar_statistics": "Statistics", "navBar_notes": "Notes", "navBar_settings": "Settings", "settings_dark_mode": "Dark", "settings_light_mode": "Light", "settings_system_mode": "System", "settings_moreSettings": "More Settings", "settings_appearance": "Appearance", "settings_appearance_theme": "Theme", "settings_appearance_themeColor": "Theme Color", "settings_appearance_display": "Display", "settings_appearance_language": "Language", "settings_appearance_open_book_animation": "Open Book Animation", "settings_appearance_bottom_navigator_show": "Bottom Navigator", "reading_contents": "Contents", "statistic_to_present": "To Present", "statistic_books_read": "{count, plural, =0{No books read} =1{1 book read} other{{count} books read} }", "statistic_days_of_reading": "{count, plural, =0{No days read} =1{1 day read} other{{count} days read} }", "statistic_notes": "{count, plural, =0{No notes} =1{1 note} other{{count} notes} }", "statistic_week": "week", "statistic_month": "month", "statistic_year": "year", "statistic_all": "all", "statistic_all_time": "All Time", "statistic_this_week": "This Week", "statistic_monday": "Mn", "statistic_tuesday": "Tu", "statistic_wednesday": "Wd", "statistic_thursday": "Th", "statistic_friday": "Fr", "statistic_saturday": "Sa", "statistic_sunday": "Su", "notes_notes_across": "{count, plural, =0{No notes} =1{1 note} other{{count} notes} } across", "notes_books": "{count, plural, =0{No books} =1{1 book} other{{count} books} }", "notes_notes": "{count, plural, =0{No notes} =1{1 note} other{{count} notes} }", "notes_read_percentage": "{percentage} read", "reading_page_copy": "Copy", "reading_page_excerpt": "Excerpt", "reading_page_theme": "Theme", "reading_page_style": "Style", "reading_page_other": "Other", "reading_page_reading": "Reading", "reading_page_convert_chinese": "Convert Chinese", "reading_page_original": "Original", "reading_page_simplified": "Simplified", "reading_page_traditional": "Traditional", "reading_page_convert_chinese_tips": "Convert Chinese may not be accurate, please use it carefully", "reading_page_column_count": "<PERSON><PERSON><PERSON><PERSON>", "reading_page_auto": "Auto", "reading_page_single": "Single", "reading_page_double": "Double", "reading_page_bionic_reading": "Bionic Reading", "reading_page_bionic_reading_tips": "What is Bionic Reading?", "reading_page_header_settings": "Head<PERSON>", "reading_page_footer_settings": "<PERSON><PERSON>s", "reading_page_left": "Left", "reading_page_center": "Center", "reading_page_right": "Right", "reading_page_share_template": "Template", "reading_page_share_font": "Font", "reading_page_share_color": "Color", "reading_page_share_background": "Background", "reading_page_share_save": "Save", "reading_page_share_share": "Share", "reading_page_share_template_classic": "Classic", "reading_page_share_template_simple": "Simple", "reading_page_share_template_elegant": "Elegant", "reading_page_share_template_modern": "Modern", "context_menu_add_note_tips": "Add your note", "context_menu_copy": "Copy", "context_menu_search": "Search", "context_menu_translate": "Translate", "context_menu_write_idea": "Write Idea", "context_menu_delete": "Delete", "context_menu_confirm": "Confirm", "context_menu_highlight": "Highlight", "context_menu_underline": "Underline", "context_menu_share": "Share", "reading_page_full_screen": "Full Screen", "reading_page_screen_timeout": "Screen Timeout", "reading_page_page_turning_method": "Page Turning Method", "font": "Font", "add_new_font": "Add New Font", "follow_book": "Follow Book", "system_font": "System Font", "page_turning_style": "Page Turning Style", "no_animation": "No Animation", "slide": "Slide", "scroll": "<PERSON><PERSON>", "book_deleted": "Book deleted", "import_n_books_selected": "{count, plural, =1{1 book selected} other{{count} books selected}}", "import_support_types": "Support types: {types}", "import_n_books_not_support": "{count, plural, =1{1 book not support yet} other{{count} books not support yet}}", "import_import_n_books": "{count, plural, =1{Import 1 book} other{Import {count} books}}", "bookshelf_tips_1": "There are no books.", "bookshelf_tips_2": "Click the add button to add a book!", "statistics_tips_1": "No reading in the selected time period.", "statistics_tips_2": "A book is a dream that you hold in your hands.", "notes_tips_1": "There are no notes.", "notes_tips_2": "Add a note while reading!", "reading_page_chapter_pages": "Chapter Pages", "reading_page_current_page": "Current Page", "reading_page_auto_translate_selection": "Auto Translate Selection", "translate_error": "Translate E<PERSON>r", "reading_page_summary_the_chapter": "Summary the chapter", "reading_page_summary_previous_content": "Summary the previous content", "reading_page_auto_summary_previous_content": "Auto Summary Previous Content", "reading_page_at_least_two_themes": "At least 2 themes are required", "reading_page_auto_adjust_reading_theme": "Auto Adjust Reading Theme", "reading_page_auto_adjust_reading_theme_tips": "Use the 1st and 2nd colors in the color table as the day and night modes respectively", "reading_page_volume_key_turn_page": "Volume Key Turn Page", "reading_page_font_size": "Font Size", "reading_page_line_spacing": "Line Spacing", "reading_page_paragraph_spacing": "Paragraph Spacing", "reading_page_indent": "Indent", "reading_page_indent_no_change": "No change", "reading_page_side_margin": "<PERSON>gin", "reading_page_top_margin": "Top Margin", "reading_page_bottom_margin": "Bottom Margin", "reading_page_font_weight": "Font Weight", "reading_page_letter_spacing": "Letter Spacing", "book_detail_save": "Save", "book_detail_edit": "Edit", "book_detail_nth_book": "{count, plural, =1{1st book you've read} =2{2nd book you've read} few{3rd book you've read} other{{count}th book you've read}}", "book_detail_last_read_date": "Last read: ", "book_detail_import_date": "Import date: ", "reading_page_reading_info": "Reading Info", "reading_page_reading_info_chapter_title": "Chapter Title", "reading_page_reading_info_battery": "Battery", "reading_page_reading_info_time": "Time", "reading_page_reading_info_battery_and_time": "Battery and Time", "reading_page_reading_info_chapter_progress": "Chapter Progress", "reading_page_reading_info_book_progress": "Book Progress", "bookshelf_dragging": "Release to import", "bookshelf_sort": "Sort", "bookshelf_title": "Title", "bookshelf_author": "Author", "bookshelf_lastReadTime": "Last Read Time", "bookshelf_progress": "Progress", "bookshelf_importTime": "Import Time", "notes_page_detail": "Detail", "notes_page_export": "Export", "notes_page_copied": "<PERSON>pied", "notes_page_exported_to": "Exported to", "notes_page_sort_time": "By time", "notes_page_sort_chapter": "By chapter", "notes_page_filter_reset": "Reset", "notes_page_view_all_n_notes": "{count, plural, =0{No notes qualified} =1{View 1 note} other{View all {count} notes}}", "common_delete": "Delete", "common_hours_full": "{count, plural, =1{1 hour} other{{count} hours} }", "common_hours": "{count, plural, other{{count} h} }", "common_minutes_full": "{count, plural, =1{1 minute} other{{count} minutes} }", "common_minutes": "{count, plural, other{{count} m} }", "common_seconds_full": "{count, plural, =1{1 second} other{{count} seconds} }", "common_seconds": "{count, plural, other{{count} s} }", "common_save": "Save", "common_cancel": "Cancel", "common_ok": "OK", "common_success": "Success", "common_failed": "Failed", "common_uploading": "Uploading", "common_downloading": "Downloading", "common_copy": "Copy", "common_new_version": "New version!", "common_update": "Update", "common_no_new_version": "No new version", "common_confirm": "Confirm", "common_canceled": "Canceled", "common_attention": "Attention", "common_saving": "Saving", "common_saved": "Saved", "common_test": "Test", "common_dissolve": "Dissolve", "common_edit": "Edit", "common_nth_week": "{count, plural, =1{1st week} =2{2nd week} few{3rd week} other{{count}th week}}", "common_apply": "Apply", "common_reset": "Reset", "common_none": "None", "common_error": "Error", "common_undo": "Undo", "common_download": "Download", "common_pause": "Pause", "common_resume": "Resume", "common_download_failed": "Download Failed", "common_retry": "Retry", "common_ascending": "Ascending", "common_descending": "Descending", "storage": "Storage", "storage_info": "Storage Info", "storage_total_size": "Total Size", "storage_database_file": "Database File", "storage_log_file": "Log File", "storage_cache_file": "Cache <PERSON>", "storage_data_file": "Data File", "storage_book_file": "Book File", "storage_cover_file": "Cover File", "storage_font_file": "Font File", "storage_data_file_details": "Data File Details", "storage_clear_cache": "<PERSON>ache", "service_import_success": "Import success", "service_import_n_books": "{count, plural, =1{importing 1 book} other{importing {count} books}}", "webdav_webdav_not_enabled": "WebDAV is not enabled", "webdav_syncing": "Syncing", "webdav_syncing_files": "Syncing files", "webdav_sync_complete": "Sync complete", "webdav_connection_success": "Connection success", "webdav_connection_failed": "Connection failed", "webdav_set_info_first": "Please set WebDAV information first", "webdav_choose_Sources": "Choose Sources", "webdav_download": "Download from WebDAV", "webdav_upload": "Upload to WebDAV", "webdav_only_wifi": "Only sync when WiFi is connected", "webdav_sync_aborted": "Sync aborted", "webdav_sync_aborted_content": "Failed to get data from WebDAV, remote data is empty. Please check the following: \n1. Network connection \n2. Ensure that valid reading data has been uploaded to WebDAV on other devices\n\nNext, please try to upload data to WebDAV", "webdav_sync_direction": "WebDAV data may be modified by other devices, please choose how to handle the data", "settings_sync": "Sync", "settings_sync_webdav": "WebDAV", "settings_sync_enable_webdav": "Enable WebDAV", "settings_sync_webdav_url": "WebDAV URL", "settings_sync_webdav_username": "Username", "settings_sync_webdav_password": "Password", "settings_sync_webdav_test_connection": "Test Connection", "settings_sync_webdav_sync_now": "Sync Now", "export_and_import": "Export/Import", "export_and_import_export": "Export", "export_and_import_import": "Import", "exporting": "Exporting", "importing": "Importing", "export_to": "Export to {path}", "import_cannot_get_file_path": "Cannot get file path", "import_success_restart_app": "Import success, please restart the app", "import_failed": "Import failed: {error}", "settings_sync_completed_toast": "Show toast when sync completed", "settings_sync_auto_sync": "Auto sync", "settings_translate": "Translate", "settings_translate_current_service": "Current Service", "settings_translate_auto": "Auto", "settings_translate_from": "From", "settings_translate_to": "To", "settings_narrate": "Narrate", "settings_narrate_voice": "Voice", "settings_narrate_voice_model": "Voice Model", "settings_narrate_voice_model_current_model": "Current Model", "settings_narrate_voice_model_not_selected": "Not Selected", "settings_narrate_voice_model_click_to_view": "Click to View", "settings_narrate_voice_model_male": "Male", "settings_narrate_voice_model_female": "Female", "settings_ai": "AI", "settings_ai_services": "Services", "settings_ai_prompt": "Prompt", "settings_ai_prompt_test": "Test AI config", "settings_ai_prompt_summary_the_chapter": "Summary the chapter", "settings_ai_prompt_summary_the_book": "Summary the book", "settings_ai_prompt_summary_the_previous_content": "Summary the previous content", "settings_ai_cache": "<PERSON>", "settings_ai_cache_size": "<PERSON>", "settings_ai_cache_current_size": "Current {count, plural, other{{count} caches}}", "settings_ai_cache_clear": "Clear cache", "ai_service_not_configured": "Please configure the AI service in settins page.", "ai_chat": "Cha<PERSON>", "ai_hint_text": "Start chatting", "ai_hint_input_placeholder": "Input message...", "ai_hint_collapse": "Collapse", "ai_hint_expand": "Expand", "ai_quick_prompt_explain": "Explain", "ai_quick_prompt_explain_text": "Please explain", "ai_quick_prompt_opinion": "Your opinion", "ai_quick_prompt_opinion_text": "What's your opinion on this?", "ai_quick_prompt_summary": "Summarize", "ai_quick_prompt_summary_text": "Please summarize", "ai_quick_prompt_analyze": "Analyze", "ai_quick_prompt_analyze_text": "Please analyze", "ai_quick_prompt_suggest": "Suggest", "ai_quick_prompt_suggest_text": "Any suggestions?", "settings_reading": "Reading", "settings_bookshelf": "Bookshelf", "settings_bookshelf_cover": "Book cover", "settings_bookshelf_cover_width": "Book cover width", "ai_cached_by": "Cached by {service}", "ai_regenerate": "Regenerate", "storage_permission_denied": "Storage permission denied", "gallery_permission_denied": "Gallery permission denied", "goto_authorize": "Go to authorize", "settings_advanced": "Advanced", "settings_advanced_log": "Log", "settings_advanced_log_clear_log": "Clear Log", "settings_advanced_log_export_log": "Export Log", "settings_advanced_clear_log_when_start": "Clear log when start", "about_check_for_updates": "Check for updates", "update_new_version": "New version: ", "update_current_version": "Current version: ", "about_privacy_policy": "Privacy Policy", "about_terms_of_use": "Terms of Use", "tts_volume": "Volume", "tts_rate": "Rate", "tts_pitch": "Pitch", "tts_narrator": "Narrator", "tts_stop_after": "{count, plural, =0{No timer} =1{Stop in 1 minute} other{stop after {count} minutes}}", "tts_type": "TTS Type", "tts_type_internal": "Internal TTS", "tts_type_system": "System TTS", "statistic_just_now": "just now", "statistic_minutes_ago": "{count, plural, =1{1 minute ago} other{{count} minutes ago}}", "statistic_hours_ago": "{count, plural, =1{1 hour ago} other{{count} hours ago}}", "statistic_yesterday": "{count, plural, =1{1 yesterday} other{{count} yesterday}}", "statistic_days_ago": "{count, plural, =1{1 day ago} other{{count} days ago}}", "statistic_months_ago": "{count, plural, =1{1 month ago} other{{count} months ago}}", "statistic_years_ago": "{count, plural, =1{1 year ago} other{{count} years ago}}", "statistic_deleted_records": "Records to be deleted", "statistic_deleted_records_tips": "Will be deleted when leaving this page", "webview_unsupported_version": "Unsupported Webview Version", "webview_unsupported_message": "Your Webview version may be not supported. Current version is {version}, required minimum version is {minVersion}, please update your Webview", "webview_update": "How to update", "webview_cancel": "I know", "webview2_not_installed": "Failed to find an installed WebView2 Runtime or non-stable Microsoft Edge installation.", "webview2_install": "Go to diownload", "iap_page_title": "Activate Product", "iap_page_restore": "Restore Purchase", "iap_page_why_choose": "Why Choose An<PERSON>", "iap_page_feature_ai": "AI Reading", "iap_page_feature_ai_desc": "AI-powered summaries, Q&A for effortless reading", "iap_page_feature_sync": "Cross-platform Sync", "iap_page_feature_sync_desc": "Keep your progress and data in sync", "iap_page_feature_stats": "Detailed Statistics", "iap_page_feature_stats_desc": "Reading time, heatmap and more", "iap_page_feature_custom": "Customize Everything", "iap_page_feature_custom_desc": "Font, color, size, spacing and more", "iap_page_feature_note": "Powerful Notes", "iap_page_feature_note_desc": "Categorize, filter, sort, export to md, csv, txt and more", "iap_page_feature_rich": "Rich <PERSON>", "iap_page_feature_rich_desc": "Notes, narration, translation", "iap_page_restore_hint": "If you change device or reinstall, click 'Restore Purchase' in the top right corner.\nOnly valid for devices under the current Apple ID", "iap_page_one_time_purchase": "Continue", "iap_page_lifetime_hint": "One-time payment, {price} lifetime access", "iap_page_status_purchased": "Thank you for your support! Your product has been successfully activated", "iap_page_status_trial": "You have {days} days left in your trial", "iap_page_status_trial_expired": "Purchase lifetime version to continue reading", "iap_page_status_original": "You are our early user, thank you for your support!", "iap_page_status_unknown": "Unable to determine your activation status", "iap_page_date_purchased": "Purchase date: {date}", "iap_page_date_trial_start": "Trial started: {date}", "iap_page_date_original": "Join date: {date}", "iap_status_purchased": "Lifetime Premium User", "iap_status_trial": "Trial Period", "iap_status_trial_expired": "Trial Expired", "iap_status_original": "Early User", "iap_status_unknown": "Unknown", "download_fonts": "Download Fonts", "font_no_available_fonts": "No available fonts", "font_official_website": "Official Website", "font_license_agreement": "License Agreement", "font_downloaded": "Downloaded", "font_failed_to_load_fonts": "Failed to load fonts", "font_downloading": "Downloading {progress}", "font_cancelled": "Cancelled {progress}", "book_sync_status_not_syncing": "Not syncing", "book_sync_status_uploading_title": "Uploading", "book_sync_status_downloading_title": "Downloading", "book_sync_status_local_update_time": "Local data update time:", "book_sync_status_last_sync_time": "Last sync time:", "book_sync_status_no_sync_yet": "This device has not synced data yet", "book_sync_status_local_only_books": "Local only books:", "book_sync_status_remote_only_books": "Remote only books:", "book_sync_status_both_books": "Books on both ends:", "book_sync_status_non_existent_books": "Neither local nor remote:", "book_sync_status_non_existent_tip": "Neither local nor remote: This means the book exists on the bookshelf, but the book file is missing on both this device and the remote storage. Syncing from a device that has the file is required.", "book_sync_status_upload_button": "Upload", "book_sync_status_download_button": "Download", "book_sync_status_release_space": "Release Space", "book_sync_status_release_space_dialog_title": "Release Space", "book_sync_status_release_space_dialog_content": "This will upload the book to the cloud and delete the local file, saving local storage space. You can download it anytime when needed.", "book_sync_status_do_not_show_again": "Do not show again", "book_sync_status_webdav_connection_failed": "WebDAV connection failed\n{error}", "book_sync_status_webdav_ping_failed": "WebDAV connection failed, ping failed", "book_sync_status_webdav_check_network": "WebDAV connection failed, check your network", "book_sync_status_webdav_connection_error": "WebDAV connection failed, connection error", "book_sync_status_sync_failed": "Sync failed", "book_sync_status_book_not_found_remote": "Could not find this book in the cloud", "book_sync_status_downloading_book": "Downloading {fileName}", "book_sync_status_space_released": "Space released", "book_sync_status_upload_failed": "Upload failed", "book_sync_status_books_count": "{count} books", "book_sync_status_download_failed": "Download failed", "webdavBatchDownloadFinishedReport": "Books downloaded, {success} success, {failed} failed", "downloadAllBooks": "Download All Books", "allBooksAreDownloaded": "All books are downloaded", "sync_now": "Sync Now", "deepl_key_tip": "Please enter your DeepL API key, which can be obtained from the DeepL developer page", "sync_mismatch_tip": "Database version mismatch. The remote database (version {remoteVersion}) is newer than what your app supports (version {currentDbVersion}). Please update your app to the latest version.", "db_updated_tip": "The database structure has been updated. Please update Anx Reader App on your other devices to ensure compatibility with the new database for smooth data synchronization.", "e_ink_mode": "E-ink Mode", "reading_page_writing_direction": "Writing Direction", "reading_page_writing_direction_auto": "Auto", "reading_page_writing_direction_vertical": "Vertical", "reading_page_writing_direction_horizontal": "Horizontal", "reading_page_style_background": "Background", "reading_page_style_no_background_image": "No Background Image", "reading_page_style_import_background_image": "Import Background Image", "reading_page_verticle_margin": "Vertical Margin", "reading_page_right_margin": "Right Margin", "reading_page_left_margin": "Left Margin", "sync_remote_data_update_time": "Remote data update time: ", "settings_translate_ai_tip": "Go to the AI settings page to configure the AI service.", "settings_ai_prompt_translate_and_dictionary": "Translate And Dictionary", "note_list_show_bookmark": "Show Bookmark", "reading_bookmark": "Bookmark", "no_bookmarks": "No Bookmarks", "no_bookmarks_tip": "Pull down or click the bookmark icon in the top-right corner to add a bookmark.", "settings_advanced_javascript": "JavaScript", "settings_advanced_enable_javascript_for_epub": "Enable JavaScript for Epub", "reading_settings_margin": "<PERSON><PERSON>", "update_via_github": "Via GitHub", "update_via_official_website": "Via Official Website", "testingConnection": "Testing connection...", "unknownErrorWhenTestingConnection": "Unknown error when testing connection: ", "configurationInformationIsIncomplete": "Configuration information is incomplete", "connectionSuccessful": "Connection successful", "connectionTimeout": "Connection timeout, please check network or server address", "testUnauthorized": "Username or password is incorrect", "testForbidden": "Access denied, please check permission settings", "testNotFound": "Server address does not exist or path is incorrect", "testRefused": "Unable to connect to server, please check network and server status", "testSsl": "SSL certificate verification failed, please check HTTPS configuration", "testDnd": "DNS resolution failed, please check if domain name is correct", "testOther": "Connection test failed, please check configuration and network", "database_backup_management": "Database Backup Management", "available_backups": "Available Backups:", "no_backups_available": "No backups available", "restore": "Rest<PERSON>", "confirmRestore": "Confirm <PERSON>ore", "restoreWarning": "This operation will replace the current database with the backup file. Current unsynced data may be lost. Do you want to continue?", "restoreSuccess": "Database restored successfully", "downloadFailed": "Download Failed", "downloadFailedContent": "Unable to download database file from server. Please check network connection and server status.", "syncValidationFailed": "Data Validation Failed", "syncValidationFailedContent": "Downloaded database file validation failed, possibly corrupted or empty. Local data has been preserved.", "replacementFailed": "Replacement Failed", "replacementFailedContent": "Database validation failed after replacement, automatically recovered to previous version. Your data is safe.", "syncUnknownError": "Sync Error", "onboarding_welcome_title": "Welcome to Anx Reader", "onboarding_welcome_body": "A powerful e-book reader with AI integration and cross-platform sync capabilities.", "onboarding_appearance_title": "Customize Your Experience", "onboarding_appearance_body": "Choose your preferred language, theme colors, and e-ink mode for the best reading experience. You can configure more display options in Settings → Appearance.", "onboarding_sync_title": "Cross-Platform Sync", "onboarding_sync_body": "Sync your books, reading progress, and notes across all your devices using WebDAV.", "onboarding_sync_tip": "Set up sync in Settings → Sync to get started", "onboarding_ai_title": "AI-Powered Features", "onboarding_ai_body": "Enhance your reading with AI-powered translation, content analysis, and chat features.", "onboarding_ai_tip": "Configure your preferred AI provider in Settings → AI", "onboarding_complete_title": "Ready to Read!", "onboarding_complete_body": "You're all set! Start adding books to your library and enjoy a premium reading experience.", "onboarding_next": "Next", "onboarding_back": "Back", "onboarding_done": "Get Started", "onboarding_skip": "<PERSON><PERSON>", "customizeYourExperience": "Customize your experience", "moreDisplayOptionsTip": "More display options available in Settings → Appearance", "optimizedForEInkDisplays": "Optimized for e-ink displays", "common_updated": "Updated!", "update_from_version": "Updated from {version}", "welcome_to_version": "Welcome to {version}", "whats_new": "What's New", "databaseBackupManagement": "Database Backup Management", "restoreDatabase": "Restore Database", "restoreBackup": "Restore Backup", "custom_css_enabled": "Enable Custom CSS", "css_validation_unmatched_braces": "Unmatched braces", "css_validation_missing_semicolon": "Missing semicolon on line {lineNumber}", "css_save_and_apply": "Save & Apply", "css_restore_default": "<PERSON><PERSON>", "css_editor_hint": "Your custom CSS here..."}