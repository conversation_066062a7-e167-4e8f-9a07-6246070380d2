// import 'dart:io';
//
// import 'package:anx_reader/service/book_player/book_player.dart';
// import 'package:epubx/epubx.dart';
//
// getFilePageTest() async {
//       String path = '/home/<USER>/other/白夜行.epub';
//       EpubBook epubBook = await EpubReader.readBook(File(path).readAsBytesSync());
//       for (int i = 0; i < epubBook.Chapters!.length; i++) {
//         // EpubChapter chapter = epubBook.Chapters![i];
//       final file = getChapterFileName(epubBook, i);
//
//         // print(file);
//         // print(chapter.HtmlContent);
//       }
// }
