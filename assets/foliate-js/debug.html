<!DOCTYPE html>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
<title>Anx Reader Debug</title>
<style>
  html {
    height: 100vh;
    overflow: hidden;
  }

  body {
    user-select: none;
    margin: 0 !important;
    height: 100vh;
  }

  #footnote-dialog {
    position: fixed;
    width: 80vw;
    height: 80vh;
    max-width: 400px;
    max-height: 200px;
    min-width: 300px;
    min-height: 200px;
    border-radius: 15px;
    border: 1px solid grey;
    user-select: none;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    outline: none;
    z-index: 1000;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
  }

  #footnote-dialog main {
    overflow: auto;
    width: 100%;
    height: 100%;
  }

  #debug-controls {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 5px;
    z-index: 2000;
  }
</style>

<!-- <div id="debug-controls">
  <button onclick="window.prevPage()">Prev</button>
  <button onclick="window.nextPage()">Next</button>
  <button onclick="window.goToPercent(0.5)">50%</button> -->
  <!-- <input type="file" id="bookFile" accept=".epub,.mobi,.azw3,.fb2,.txt,.pdf"> -->
<!-- </div> -->

<div id="footnote-dialog">
  <main></main>
</div>

<script>
  console.log("AnxUA", navigator.userAgent);

  const urlParams = new URLSearchParams(window.location.search);
  if (!urlParams.has('url')) {
    const mockParams = {
      importing: false,
      url: '../local/alice.epub',
      initialCfi: null,
      style: {
        fontSize: 1.4,
        fontName: "book",
        fontPath: "",
        fontWeight: 400.0,
        letterSpacing: 0.0,
        spacing: 1.5,
        paragraphSpacing: 5.0,
        textIndent: 0.0,
        fontColor: "#111111ff",
        backgroundColor: "#fefefeff",
        topMargin: 20.0,
        bottomMargin: 50.0,
        sideMargin: 15.0,
        justify: true,
        hyphenate: true,
        // 'slide' 'scroll' 'noAnimation'
        pageTurnStyle: "scroll",
        // pageTurnStyle: "slide",
        maxColumnCount: 0,
        // auto 'horizontal-tb' 'vertical-rl'
        // writingMode: "vertical-rl",
        writingMode: "horizontal-tb",
        backgroundImage: "none",
        // true, false
        allowScript: true,
      },
      readingRules: {
        convertChineseMode: "none",
        bionicReadingMode: false
      }
    };

    const params = new URLSearchParams();
    params.set('importing', JSON.stringify(mockParams.importing));
    params.set('url', JSON.stringify(mockParams.url));
    params.set('initialCfi', JSON.stringify(mockParams.initialCfi));
    params.set('style', JSON.stringify(mockParams.style));
    params.set('readingRules', JSON.stringify(mockParams.readingRules));

    window.location.href = window.location.pathname + '?' + params.toString();
  }

  window.flutter_inappwebview = {
    callHandler: (name, data) => {
      console.log(`Flutter Handler Called: ${name}`, data);
    }
  };

    const anno = [
    // { id: 1, type: 'highlight', value: "epubcfi(/6/6!/4/2[pgepubid00003],/38/9:0,/42/1:408)", color: 'blue', note: 'this is' },
    // { id: 2, type: 'bookmark', value: "epubcfi(/6/6!/4/2[pgepubid00003],/42/1:408,/44/1:345)", color: '0.02', note: 'this is' },
    // { id: 3, type: 'underline', value: "epubcfi(/6/4!/4/4,/1:294,/1:301)", color: 'red', note: 'this is' },
  ]

  setTimeout(() => {
    reader.renderAnnotation(anno)
  }, 1000)
</script>

<script src="./dist/bundle.js" type="module"></script>
<script src="./dist/pdf-legacy.js"></script>
</html>