# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

linter:
  # DassoShu Reader - Design System Enforcement Rules
  # Professional Flutter development with mandatory design system compliance
  rules:
    # === DESIGN SYSTEM ENFORCEMENT ===
    # These rules ensure proper usage of our dual design system architecture

    # Code Quality & Performance
    prefer_const_constructors: true
    prefer_const_declarations: true
    prefer_const_literals_to_create_immutables: true
    avoid_unnecessary_containers: true
    sized_box_for_whitespace: true
    use_key_in_widget_constructors: true
    require_trailing_commas: true
    prefer_single_quotes: true

    # Performance Optimization
    avoid_function_literals_in_foreach_calls: true
    prefer_collection_literals: true
    prefer_spread_collections: true

    # Professional Code Standards
    always_declare_return_types: true
    annotate_overrides: true
    avoid_empty_else: true
    avoid_returning_null_for_void: true
    camel_case_types: true
    constant_identifier_names: true
    empty_constructor_bodies: true
    implementation_imports: true
    library_names: true
    non_constant_identifier_names: true
    package_names: true
    prefer_adjacent_string_concatenation: true
    prefer_interpolation_to_compose_strings: true
    unnecessary_brace_in_string_interps: true
    unnecessary_const: true
    unnecessary_new: true
    unnecessary_null_in_if_null_operators: true
    unnecessary_this: true
    use_rethrow_when_possible: true

    # Development Best Practices
    avoid_print: true
    use_build_context_synchronously: true

analyzer:
  plugins:
    - custom_lint
  exclude:
    - "lib/l10n/generated/*"
    - "**/*.g.dart"
    - "**/*.freezed.dart"

  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

  errors:
    invalid_annotation_target: ignore
    # Design System Enforcement Errors (Keep as errors)
    prefer_const_constructors: error
    avoid_unnecessary_containers: error
    sized_box_for_whitespace: error
    use_build_context_synchronously: error

    # Cross-Platform Compatibility Errors
    # These will be enforced by our custom lint rules

    # Type Safety Issues (Downgrade to warnings for now)
    invalid_assignment: warning
    argument_type_not_assignable: warning
    return_of_invalid_type: warning
    non_bool_condition: warning
    non_bool_operand: warning
    list_element_type_not_assignable: warning
    map_value_type_not_assignable: warning
    set_element_type_not_assignable: warning

# Custom lint configuration for cross-platform development
# Note: Custom lint rules are available but currently disabled
# Enable when custom_lint_builder is properly configured
# custom_lint:
#   rules:
#     - avoid_platform_specific_imports
#     - prefer_adaptive_widgets
#     - enforce_design_system_usage

  language:
    strict-casts: true
    strict-inference: true
    strict-raw-types: true

# === DESIGN SYSTEM ENFORCEMENT CONFIGURATION ===
# Custom rules to ensure proper usage of DesignSystem + Pixel-Perfect architecture

# Custom lint rules would be implemented here when custom_lint package is properly configured
# For now, relying on standard Flutter lint rules above
# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
