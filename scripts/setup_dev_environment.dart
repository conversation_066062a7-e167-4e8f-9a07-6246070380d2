#!/usr/bin/env dart

/// Development environment setup script for DassoShu Reader
/// 
/// Sets up all cross-platform development tools, validation scripts,
/// and IDE configurations for optimal development experience.

import 'dart:io';
import 'dart:convert';

void main(List<String> args) async {
  print('🚀 DassoShu Reader Development Environment Setup');
  print('================================================');

  final setup = DevEnvironmentSetup();

  try {
    await setup.run();
    print('\n✅ Development environment setup complete!');
    print('\n🎯 Next steps:');
    print('1. Start development with: dart scripts/dev_validation.dart --watch');
    print('2. Run cross-platform analysis: dart scripts/cross_platform_analyzer.dart');
    print('3. Check custom lint rules: dart run custom_lint');
  } catch (e) {
    print('❌ Setup failed: $e');
    exit(1);
  }
}

class DevEnvironmentSetup {
  /// Run the complete setup process
  Future<void> run() async {
    await _checkPrerequisites();
    await _setupGitHooks();
    await _setupVSCodeConfig();
    await _validateScripts();
    await _runInitialValidation();
  }

  /// Check that all prerequisites are installed
  Future<void> _checkPrerequisites() async {
    print('\n🔍 Checking prerequisites...');

    // Check Flutter
    final flutterResult = await Process.run('flutter', ['--version']);
    if (flutterResult.exitCode != 0) {
      throw Exception('Flutter not found. Please install Flutter first.');
    }
    print('✅ Flutter found');

    // Check Dart
    final dartResult = await Process.run('dart', ['--version']);
    if (dartResult.exitCode != 0) {
      throw Exception('Dart not found. Please install Dart first.');
    }
    print('✅ Dart found');

    // Check if we're in a Flutter project
    final pubspecFile = File('pubspec.yaml');
    if (!pubspecFile.existsSync()) {
      throw Exception('Not in a Flutter project directory');
    }
    print('✅ Flutter project detected');

    // Check custom_lint dependency
    final pubspecContent = await pubspecFile.readAsString();
    if (!pubspecContent.contains('custom_lint')) {
      print('⚠️  custom_lint not found in pubspec.yaml');
      print('   Add it to dev_dependencies for full functionality');
    } else {
      print('✅ custom_lint dependency found');
    }
  }

  /// Setup Git hooks for pre-commit validation
  Future<void> _setupGitHooks() async {
    print('\n🔗 Setting up Git hooks...');

    final gitDir = Directory('.git');
    if (!gitDir.existsSync()) {
      print('⚠️  Not a Git repository, skipping Git hooks setup');
      return;
    }

    final hooksDir = Directory('.git/hooks');
    if (!hooksDir.existsSync()) {
      await hooksDir.create();
    }

    final preCommitHook = File('.git/hooks/pre-commit');
    final hookContent = '''#!/bin/sh
echo "🔍 Running cross-platform validation..."
dart scripts/dev_validation.dart
if [ \$? -ne 0 ]; then
  echo "❌ Cross-platform validation failed. Fix issues before committing."
  exit 1
fi

echo "📋 Running custom lint rules..."
dart run custom_lint
if [ \$? -ne 0 ]; then
  echo "❌ Custom lint rules failed. Fix issues before committing."
  exit 1
fi

echo "✅ Pre-commit validation passed"
''';

    await preCommitHook.writeAsString(hookContent);
    
    // Make hook executable on Unix systems
    if (!Platform.isWindows) {
      await Process.run('chmod', ['+x', preCommitHook.path]);
    }

    print('✅ Git pre-commit hook installed');
  }

  /// Setup VS Code configuration
  Future<void> _setupVSCodeConfig() async {
    print('\n⚙️  Setting up VS Code configuration...');

    final vscodeDir = Directory('.vscode');
    if (!vscodeDir.existsSync()) {
      await vscodeDir.create();
    }

    // Settings
    final settingsFile = File('.vscode/settings.json');
    final settings = {
      'dart.analysisExcludedFolders': [
        'lib/l10n/generated'
      ],
      'dart.runPubGetOnPubspecChanges': true,
      'dart.customLintRules': true,
      'files.watcherExclude': {
        '**/.git/objects/**': true,
        '**/.git/subtree-cache/**': true,
        '**/node_modules/*/**': true,
        '**/*.g.dart': true,
        '**/*.freezed.dart': true
      },
      'dart.additionalAnalyzerFileExtensions': [],
      'dart.showTodos': true,
      'dart.previewFlutterUiGuides': true,
      'dart.previewFlutterUiGuidesCustomTracking': true
    };

    await settingsFile.writeAsString(
      const JsonEncoder.withIndent('  ').convert(settings)
    );
    print('✅ VS Code settings configured');

    // Tasks
    final tasksFile = File('.vscode/tasks.json');
    final tasks = {
      'version': '2.0.0',
      'tasks': [
        {
          'label': 'Cross-Platform Validation',
          'type': 'shell',
          'command': 'dart',
          'args': ['scripts/dev_validation.dart', '--verbose'],
          'group': 'build',
          'presentation': {
            'echo': true,
            'reveal': 'always',
            'focus': false,
            'panel': 'shared'
          }
        },
        {
          'label': 'Cross-Platform Analysis',
          'type': 'shell',
          'command': 'dart',
          'args': ['scripts/cross_platform_analyzer.dart', '--verbose'],
          'group': 'build',
          'presentation': {
            'echo': true,
            'reveal': 'always',
            'focus': false,
            'panel': 'shared'
          }
        },
        {
          'label': 'Watch Mode Validation',
          'type': 'shell',
          'command': 'dart',
          'args': ['scripts/dev_validation.dart', '--watch'],
          'group': 'build',
          'isBackground': true,
          'presentation': {
            'echo': true,
            'reveal': 'always',
            'focus': false,
            'panel': 'dedicated'
          }
        }
      ]
    };

    await tasksFile.writeAsString(
      const JsonEncoder.withIndent('  ').convert(tasks)
    );
    print('✅ VS Code tasks configured');

    // Launch configurations
    final launchFile = File('.vscode/launch.json');
    final launch = {
      'version': '0.2.0',
      'configurations': [
        {
          'name': 'Flutter (Android)',
          'request': 'launch',
          'type': 'dart',
          'program': 'lib/main.dart',
          'args': ['--flavor', 'development'],
          'deviceId': 'android'
        },
        {
          'name': 'Flutter (iOS)',
          'request': 'launch',
          'type': 'dart',
          'program': 'lib/main.dart',
          'args': ['--flavor', 'development'],
          'deviceId': 'ios'
        }
      ]
    };

    await launchFile.writeAsString(
      const JsonEncoder.withIndent('  ').convert(launch)
    );
    print('✅ VS Code launch configurations set');
  }

  /// Validate that all scripts are working
  Future<void> _validateScripts() async {
    print('\n🧪 Validating scripts...');

    // Test dev validation script
    final devValidationResult = await Process.run(
      'dart',
      ['scripts/dev_validation.dart', '--help'],
    );
    if (devValidationResult.exitCode != 0) {
      throw Exception('dev_validation.dart script not working');
    }
    print('✅ Development validation script working');

    // Test cross-platform analyzer
    final analyzerResult = await Process.run(
      'dart',
      ['scripts/cross_platform_analyzer.dart', '--help'],
    );
    if (analyzerResult.exitCode != 0) {
      throw Exception('cross_platform_analyzer.dart script not working');
    }
    print('✅ Cross-platform analyzer script working');

    // Test custom lint (if available)
    final customLintResult = await Process.run(
      'dart',
      ['run', 'custom_lint', '--help'],
    );
    if (customLintResult.exitCode == 0) {
      print('✅ Custom lint rules working');
    } else {
      print('⚠️  Custom lint not available (install custom_lint package)');
    }
  }

  /// Run initial validation to check current state
  Future<void> _runInitialValidation() async {
    print('\n🔍 Running initial validation...');

    // Run development validation
    final validationResult = await Process.run(
      'dart',
      ['scripts/dev_validation.dart'],
    );

    if (validationResult.exitCode == 0) {
      print('✅ Initial validation passed');
    } else {
      print('⚠️  Initial validation found issues:');
      print(validationResult.stdout);
      print('\nRun with --fix to automatically fix some issues:');
      print('dart scripts/dev_validation.dart --fix');
    }

    // Run Flutter analyze
    print('\n📋 Running Flutter analyze...');
    final analyzeResult = await Process.run('flutter', ['analyze']);
    
    if (analyzeResult.exitCode == 0) {
      print('✅ Flutter analyze passed');
    } else {
      print('⚠️  Flutter analyze found issues:');
      print(analyzeResult.stdout);
    }
  }
}
