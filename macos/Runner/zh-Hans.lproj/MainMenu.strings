
/* Class = "NSMenuItem"; title = "APP_NAME"; ObjectID = "1Xt-HY-uBw"; */
"1Xt-HY-uBw.title" = "APP_NAME";

/* Class = "NSMenu"; title = "Find"; ObjectID = "1b7-l0-nxx"; */
"1b7-l0-nxx.title" = "Find";

/* Class = "NSMenuItem"; title = "Transformations"; ObjectID = "2oI-Rn-ZJC"; */
"2oI-Rn-ZJC.title" = "Transformations";

/* Class = "NSMenu"; title = "Spelling"; ObjectID = "3IN-sU-3Bg"; */
"3IN-sU-3Bg.title" = "Spelling";

/* Class = "NSMenu"; title = "Speech"; ObjectID = "3rS-ZA-NoH"; */
"3rS-ZA-NoH.title" = "Speech";

/* Class = "NSMenuItem"; title = "Find"; ObjectID = "4EN-yA-p0u"; */
"4EN-yA-p0u.title" = "Find";

/* Class = "NSMenuItem"; title = "Enter Full Screen"; ObjectID = "4J7-dP-txa"; */
"4J7-dP-txa.title" = "Enter Full Screen";

/* Class = "NSMenuItem"; title = "Quit APP_NAME"; ObjectID = "4sb-4s-VLi"; */
"4sb-4s-VLi.title" = "Quit APP_NAME";

/* Class = "NSMenuItem"; title = "Edit"; ObjectID = "5QF-Oa-p0T"; */
"5QF-Oa-p0T.title" = "Edit";

/* Class = "NSMenuItem"; title = "About APP_NAME"; ObjectID = "5kV-Vb-QxS"; */
"5kV-Vb-QxS.title" = "About APP_NAME";

/* Class = "NSMenuItem"; title = "Redo"; ObjectID = "6dh-zS-Vam"; */
"6dh-zS-Vam.title" = "Redo";

/* Class = "NSMenuItem"; title = "Correct Spelling Automatically"; ObjectID = "78Y-hA-62v"; */
"78Y-hA-62v.title" = "Correct Spelling Automatically";

/* Class = "NSMenuItem"; title = "Substitutions"; ObjectID = "9ic-FL-obx"; */
"9ic-FL-obx.title" = "Substitutions";

/* Class = "NSMenuItem"; title = "Smart Copy/Paste"; ObjectID = "9yt-4B-nSM"; */
"9yt-4B-nSM.title" = "Smart Copy/Paste";

/* Class = "NSMenu"; title = "Main Menu"; ObjectID = "AYu-sK-qS6"; */
"AYu-sK-qS6.title" = "Main Menu";

/* Class = "NSMenuItem"; title = "Preferences…"; ObjectID = "BOF-NM-1cW"; */
"BOF-NM-1cW.title" = "Preferences…";

/* Class = "NSMenuItem"; title = "Spelling and Grammar"; ObjectID = "Dv1-io-Yv7"; */
"Dv1-io-Yv7.title" = "Spelling and Grammar";

/* Class = "NSMenuItem"; title = "Help"; ObjectID = "EPT-qC-fAb"; */
"EPT-qC-fAb.title" = "Help";

/* Class = "NSMenu"; title = "Substitutions"; ObjectID = "FeM-D8-WVr"; */
"FeM-D8-WVr.title" = "Substitutions";

/* Class = "NSMenuItem"; title = "View"; ObjectID = "H8h-7b-M4v"; */
"H8h-7b-M4v.title" = "View";

/* Class = "NSMenuItem"; title = "Text Replacement"; ObjectID = "HFQ-gK-NFA"; */
"HFQ-gK-NFA.title" = "Text Replacement";

/* Class = "NSMenuItem"; title = "Show Spelling and Grammar"; ObjectID = "HFo-cy-zxI"; */
"HFo-cy-zxI.title" = "Show Spelling and Grammar";

/* Class = "NSMenu"; title = "View"; ObjectID = "HyV-fh-RgO"; */
"HyV-fh-RgO.title" = "View";

/* Class = "NSMenuItem"; title = "Show All"; ObjectID = "Kd2-mp-pUS"; */
"Kd2-mp-pUS.title" = "Show All";

/* Class = "NSMenuItem"; title = "Bring All to Front"; ObjectID = "LE2-aR-0XJ"; */
"LE2-aR-0XJ.title" = "Bring All to Front";

/* Class = "NSMenuItem"; title = "Services"; ObjectID = "NMo-om-nkz"; */
"NMo-om-nkz.title" = "Services";

/* Class = "NSMenuItem"; title = "Minimize"; ObjectID = "OY7-WF-poV"; */
"OY7-WF-poV.title" = "Minimize";

/* Class = "NSMenuItem"; title = "Hide APP_NAME"; ObjectID = "Olw-nP-bQN"; */
"Olw-nP-bQN.title" = "Hide APP_NAME";

/* Class = "NSMenuItem"; title = "Find Previous"; ObjectID = "OwM-mh-QMV"; */
"OwM-mh-QMV.title" = "Find Previous";

/* Class = "NSMenuItem"; title = "Stop Speaking"; ObjectID = "Oyz-dy-DGm"; */
"Oyz-dy-DGm.title" = "Stop Speaking";

/* Class = "NSWindow"; title = "APP_NAME"; ObjectID = "QvC-M9-y7g"; */
"QvC-M9-y7g.title" = "APP_NAME";

/* Class = "NSMenuItem"; title = "Zoom"; ObjectID = "R4o-n2-Eq4"; */
"R4o-n2-Eq4.title" = "Zoom";

/* Class = "NSMenuItem"; title = "Select All"; ObjectID = "Ruw-6m-B2m"; */
"Ruw-6m-B2m.title" = "Select All";

/* Class = "NSMenuItem"; title = "Jump to Selection"; ObjectID = "S0p-oC-mLd"; */
"S0p-oC-mLd.title" = "Jump to Selection";

/* Class = "NSMenu"; title = "Window"; ObjectID = "Td7-aD-5lo"; */
"Td7-aD-5lo.title" = "Window";

/* Class = "NSMenuItem"; title = "Capitalize"; ObjectID = "UEZ-Bs-lqG"; */
"UEZ-Bs-lqG.title" = "Capitalize";

/* Class = "NSMenuItem"; title = "Hide Others"; ObjectID = "Vdr-fp-XzO"; */
"Vdr-fp-XzO.title" = "Hide Others";

/* Class = "NSMenu"; title = "Edit"; ObjectID = "W48-6f-4Dl"; */
"W48-6f-4Dl.title" = "Edit";

/* Class = "NSMenuItem"; title = "Paste and Match Style"; ObjectID = "WeT-3V-zwk"; */
"WeT-3V-zwk.title" = "Paste and Match Style";

/* Class = "NSMenuItem"; title = "Find…"; ObjectID = "Xz5-n4-O0W"; */
"Xz5-n4-O0W.title" = "Find…";

/* Class = "NSMenuItem"; title = "Find and Replace…"; ObjectID = "YEy-JH-Tfz"; */
"YEy-JH-Tfz.title" = "Find and Replace…";

/* Class = "NSMenuItem"; title = "Start Speaking"; ObjectID = "Ynk-f8-cLZ"; */
"Ynk-f8-cLZ.title" = "Start Speaking";

/* Class = "NSMenuItem"; title = "Window"; ObjectID = "aUF-d1-5bR"; */
"aUF-d1-5bR.title" = "Window";

/* Class = "NSMenuItem"; title = "Use Selection for Find"; ObjectID = "buJ-ug-pKt"; */
"buJ-ug-pKt.title" = "Use Selection for Find";

/* Class = "NSMenu"; title = "Transformations"; ObjectID = "c8a-y6-VQd"; */
"c8a-y6-VQd.title" = "Transformations";

/* Class = "NSMenuItem"; title = "Smart Links"; ObjectID = "cwL-P1-jid"; */
"cwL-P1-jid.title" = "Smart Links";

/* Class = "NSMenuItem"; title = "Make Lower Case"; ObjectID = "d9M-CD-aMd"; */
"d9M-CD-aMd.title" = "Make Lower Case";

/* Class = "NSMenuItem"; title = "Undo"; ObjectID = "dRJ-4n-Yzg"; */
"dRJ-4n-Yzg.title" = "Undo";

/* Class = "NSMenuItem"; title = "Paste"; ObjectID = "gVA-U4-sdL"; */
"gVA-U4-sdL.title" = "Paste";

/* Class = "NSMenuItem"; title = "Smart Quotes"; ObjectID = "hQb-2v-fYv"; */
"hQb-2v-fYv.title" = "Smart Quotes";

/* Class = "NSMenuItem"; title = "Check Document Now"; ObjectID = "hz2-CU-CR7"; */
"hz2-CU-CR7.title" = "Check Document Now";

/* Class = "NSMenu"; title = "Services"; ObjectID = "hz9-B4-Xy5"; */
"hz9-B4-Xy5.title" = "Services";

/* Class = "NSMenuItem"; title = "Check Grammar With Spelling"; ObjectID = "mK6-2p-4JG"; */
"mK6-2p-4JG.title" = "Check Grammar With Spelling";

/* Class = "NSMenuItem"; title = "Delete"; ObjectID = "pa3-QI-u2k"; */
"pa3-QI-u2k.title" = "Delete";

/* Class = "NSMenuItem"; title = "Find Next"; ObjectID = "q09-fT-Sye"; */
"q09-fT-Sye.title" = "Find Next";

/* Class = "NSMenu"; title = "Help"; ObjectID = "rJ0-wn-3NY"; */
"rJ0-wn-3NY.title" = "Help";

/* Class = "NSMenuItem"; title = "Check Spelling While Typing"; ObjectID = "rbD-Rh-wIN"; */
"rbD-Rh-wIN.title" = "Check Spelling While Typing";

/* Class = "NSMenuItem"; title = "Smart Dashes"; ObjectID = "rgM-f4-ycn"; */
"rgM-f4-ycn.title" = "Smart Dashes";

/* Class = "NSMenuItem"; title = "Data Detectors"; ObjectID = "tRr-pd-1PS"; */
"tRr-pd-1PS.title" = "Data Detectors";

/* Class = "NSMenu"; title = "APP_NAME"; ObjectID = "uQy-DD-JDr"; */
"uQy-DD-JDr.title" = "APP_NAME";

/* Class = "NSMenuItem"; title = "Cut"; ObjectID = "uRl-iY-unG"; */
"uRl-iY-unG.title" = "Cut";

/* Class = "NSMenuItem"; title = "Make Upper Case"; ObjectID = "vmV-6d-7jI"; */
"vmV-6d-7jI.title" = "Make Upper Case";

/* Class = "NSMenuItem"; title = "Copy"; ObjectID = "x3v-GG-iWU"; */
"x3v-GG-iWU.title" = "Copy";

/* Class = "NSMenuItem"; title = "Speech"; ObjectID = "xrE-MZ-jX0"; */
"xrE-MZ-jX0.title" = "Speech";

/* Class = "NSMenuItem"; title = "Show Substitutions"; ObjectID = "z6F-FW-3nz"; */
"z6F-FW-3nz.title" = "Show Substitutions";
