# 🌐 Cross-Platform Development Guide for DassoShu Reader

## 📋 Overview
This guide provides development-time awareness tools and best practices to prevent platform-specific issues while coding. Focus on Android and iOS mobile/tablet form factors.

## 🛠️ Development Tools

### **1. Real-Time Validation**
```bash
# Run once
dart scripts/dev_validation.dart

# Watch mode (recommended during development)
dart scripts/dev_validation.dart --watch

# Auto-fix issues
dart scripts/dev_validation.dart --fix
```

### **2. Cross-Platform Analysis**
```bash
# Comprehensive analysis
dart scripts/cross_platform_analyzer.dart --verbose

# JSON output for IDE integration
dart scripts/cross_platform_analyzer.dart --json
```

### **3. Custom Lint Rules**
```bash
# Run custom lint rules
dart run custom_lint

# Analyze specific files
flutter analyze lib/specific_file.dart
```

## 🚫 Common Platform Issues to Avoid

### **Platform Checks**
```dart
// ❌ DON'T - Direct platform checks
if (Platform.isIOS) { ... }

// ✅ DO - Use adaptive patterns
if (PlatformAdaptations.isIOS) { ... }
```

### **Navigation**
```dart
// ❌ DON'T - Platform-specific routes
Navigator.push(context, MaterialPageRoute(...));
Navigator.push(context, CupertinoPageRoute(...));

// ✅ DO - Adaptive navigation
AdaptiveNavigation.push(context, MyPage());
```

### **Dialogs**
```dart
// ❌ DON'T - Platform-specific dialogs
showDialog(context: context, builder: (_) => AlertDialog(...));

// ✅ DO - Adaptive dialogs
AdaptiveDialog.show(context, title: 'Title', content: 'Content');
```

### **Design System Values**
```dart
// ❌ DON'T - Hardcoded values
EdgeInsets.all(16.0)
BorderRadius.circular(8.0)

// ✅ DO - Design system constants
EdgeInsets.all(DesignSystem.spaceM)
BorderRadius.circular(DesignSystem.radiusM)
```

### **Responsive Design**
```dart
// ❌ DON'T - Direct MediaQuery usage
final screenWidth = MediaQuery.of(context).size.width;

// ✅ DO - Responsive system
final isTablet = ResponsiveSystem.isTablet(context);
final padding = DesignSystem.getAdaptivePadding(context);
```

### **Chinese Font Handling**
```dart
// ❌ DON'T - Generic font families for Chinese text
Text('中文', style: TextStyle(fontFamily: 'Arial'))

// ✅ DO - Chinese font library
Text('中文', style: TextStyle(fontFamily: ChineseFontLibrary.notoSansSC))
```

### **File Paths**
```dart
// ❌ DON'T - Hardcoded path separators
final path = 'assets/images/icon.png';

// ✅ DO - Cross-platform path handling
final path = path.join('assets', 'images', 'icon.png');
```

### **Network URLs**
```dart
// ❌ DON'T - localhost (iOS compatibility issues)
final url = 'http://localhost:8080/api';

// ✅ DO - Use 127.0.0.1
final url = 'http://127.0.0.1:8080/api';
```

## 🎯 IDE Integration

### **VS Code Settings**
Add to `.vscode/settings.json`:
```json
{
  "dart.analysisExcludedFolders": [
    "lib/l10n/generated"
  ],
  "dart.runPubGetOnPubspecChanges": true,
  "dart.customLintRules": true,
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/*/**": true,
    "**/*.g.dart": true,
    "**/*.freezed.dart": true
  }
}
```

### **Pre-commit Hook**
Create `.git/hooks/pre-commit`:
```bash
#!/bin/sh
echo "🔍 Running cross-platform validation..."
dart scripts/dev_validation.dart
if [ $? -ne 0 ]; then
  echo "❌ Cross-platform validation failed. Fix issues before committing."
  exit 1
fi
```

## 📱 Platform-Specific Considerations

### **Android (Mobile & Tablet)**
- **Material Design 3**: Use `useMaterial3: true`
- **Scroll Physics**: `ClampingScrollPhysics` (default)
- **Navigation**: `MaterialPageRoute` patterns
- **File Access**: Standard Android permissions
- **WebView**: Standard localhost handling

### **iOS (Mobile & Tablet)**
- **Design Guidelines**: Follow iOS HIG
- **Scroll Physics**: `BouncingScrollPhysics`
- **Navigation**: `CupertinoPageRoute` patterns  
- **File Access**: iOS sandbox restrictions
- **WebView**: Use `127.0.0.1` instead of `localhost`

### **Tablet Adaptations**
- **Screen Size**: Use `ResponsiveSystem.isTablet()`
- **Layout**: Adaptive layouts for larger screens
- **Navigation**: Consider split-view patterns
- **Touch Targets**: Ensure 44dp minimum size

## 🔧 Development Workflow

### **Daily Development**
1. **Start watch mode**: `dart scripts/dev_validation.dart --watch`
2. **Code with awareness**: Follow platform-agnostic patterns
3. **Fix issues immediately**: Address validation warnings as they appear
4. **Test on both platforms**: Use simulators/emulators regularly

### **Before Committing**
1. **Run full validation**: `dart scripts/cross_platform_analyzer.dart`
2. **Check custom lint**: `dart run custom_lint`
3. **Analyze code**: `flutter analyze`
4. **Test critical paths**: Manual testing on both platforms

### **CI/CD Integration**
- **Automated validation** runs on every push
- **Platform-specific builds** validate compatibility
- **Quality gates** prevent platform-breaking changes

## 📊 Monitoring & Metrics

### **Development Metrics**
- Cross-platform issues caught during development
- Time saved by early issue detection
- Reduction in platform-specific bugs

### **Quality Indicators**
- Zero platform-specific lint errors
- Consistent behavior across platforms
- Proper design system compliance

## 🎓 Best Practices

### **Code Organization**
- Keep platform-specific code in dedicated files
- Use dependency injection for platform services
- Abstract platform differences behind interfaces

### **Testing Strategy**
- Manual testing on both platforms
- Focus on critical user journeys
- Test on different screen sizes and orientations

### **Documentation**
- Document platform-specific decisions
- Maintain compatibility notes
- Update guides when patterns change

## 🚀 Quick Reference

### **Essential Commands**
```bash
# Development validation (watch mode)
dart scripts/dev_validation.dart --watch

# Full cross-platform analysis
dart scripts/cross_platform_analyzer.dart --verbose

# Custom lint rules
dart run custom_lint

# Flutter analysis
flutter analyze
```

### **Key Patterns**
- `PlatformAdaptations.*` for platform checks
- `AdaptiveNavigation.*` for navigation
- `DesignSystem.*` for spacing/sizing
- `ResponsiveSystem.*` for screen adaptations
- `chinese_font_library` for Chinese text

## 📞 Support

### **When Issues Arise**
1. **Check validation output**: Review specific error messages
2. **Consult this guide**: Find the correct pattern to use
3. **Test on both platforms**: Verify fixes work correctly
4. **Update documentation**: If new patterns emerge

### **Continuous Improvement**
- Validation rules evolve with project needs
- New platform issues get added to detection
- Development workflow improves over time

---

*This guide ensures consistent cross-platform development without requiring automated test files. Focus on prevention through better tooling and development practices.*
