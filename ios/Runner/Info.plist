<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Dasso Reader</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>zh_CN</string>
		<string>zh_TW</string>
		<string>zh</string>
		<string>tr</string>
	</array>
	<key>CFBundleName</key>
	<string>dasso_reader</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>We need access to your photo library to save images.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need access to your photo library to save images.</string>
	<key>NSDocumentsFolderUsageDescription</key>
	<string>We need access to documents to import EPUB books and other supported formats.</string>
	<key>NSDownloadsFolderUsageDescription</key>
	<string>We need access to downloads to import EPUB books and other supported formats.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>EPUB Document</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>CFBundleTypeIconFiles</key>
			<array/>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.idpf.epub-container</string>
				<string>com.apple.ibooks.epub</string>
			</array>
			<key>LSHandlerRank</key>
			<string>Owner</string>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>Text Document</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>CFBundleTypeIconFiles</key>
			<array/>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.plain-text</string>
				<string>public.text</string>
			</array>
			<key>LSHandlerRank</key>
			<string>Owner</string>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>Mobipocket Document</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>CFBundleTypeIconFiles</key>
			<array/>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>mobi</string>
				<string>azw3</string>
			</array>
			<key>LSHandlerRank</key>
			<string>Owner</string>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>FictionBook Document</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>CFBundleTypeIconFiles</key>
			<array/>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>fb2</string>
			</array>
			<key>LSHandlerRank</key>
			<string>Owner</string>
		</dict>
	</array>
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeIdentifier</key>
			<string>org.idpf.epub-container</string>
			<key>UTTypeDescription</key>
			<string>Electronic Publication (EPUB)</string>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
				<string>public.composite-content</string>
			</array>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>epub</string>
				</array>
				<key>public.mime-type</key>
				<array>
					<string>application/epub+zip</string>
				</array>
			</dict>
		</dict>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>AppGroupId</key>
	<string>$(CUSTOM_GROUP_ID)</string>
	<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>ShareMedia-$(PRODUCT_BUNDLE_IDENTIFIER)</string>
				</array>
			</dict>
		</array>
</dict>
</plist>
