PODS:
  - audio_service (0.0.1):
    - Flutter
    - FlutterMacOS
  - audio_session (0.0.1):
    - FlutterMacOS
  - audioplayers_darwin (0.0.1):
    - FlutterMacOS
  - battery_plus (0.0.1):
    - FlutterMacOS
  - connectivity_plus (0.0.1):
    - FlutterMacOS
  - desktop_drop (0.0.1):
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - file_picker (0.0.1):
    - FlutterMacOS
  - flutter_inappwebview_macos (0.0.1):
    - FlutterMacOS
    - OrderedSet (~> 6.0.3)
  - flutter_tts (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - OrderedSet (6.0.3)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - screen_retriever_macos (0.0.1):
    - FlutterMacOS
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.49.1):
    - sqlite3/common (= 3.49.1)
  - sqlite3/common (3.49.1)
  - sqlite3/dbstatvtab (3.49.1):
    - sqlite3/common
  - sqlite3/fts5 (3.49.1):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.49.1):
    - sqlite3/common
  - sqlite3/rtree (3.49.1):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - FlutterMacOS
    - sqlite3 (~> 3.49.1)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS
  - window_manager (0.2.0):
    - FlutterMacOS

DEPENDENCIES:
  - audio_service (from `Flutter/ephemeral/.symlinks/plugins/audio_service/darwin`)
  - audio_session (from `Flutter/ephemeral/.symlinks/plugins/audio_session/macos`)
  - audioplayers_darwin (from `Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/macos`)
  - battery_plus (from `Flutter/ephemeral/.symlinks/plugins/battery_plus/macos`)
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - desktop_drop (from `Flutter/ephemeral/.symlinks/plugins/desktop_drop/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - file_picker (from `Flutter/ephemeral/.symlinks/plugins/file_picker/macos`)
  - flutter_inappwebview_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos`)
  - flutter_tts (from `Flutter/ephemeral/.symlinks/plugins/flutter_tts/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - in_app_purchase_storekit (from `Flutter/ephemeral/.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - screen_retriever_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin`)
  - sqlite3_flutter_libs (from `Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)

SPEC REPOS:
  trunk:
    - OrderedSet
    - sqlite3

EXTERNAL SOURCES:
  audio_service:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_service/darwin
  audio_session:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_session/macos
  audioplayers_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/macos
  battery_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/battery_plus/macos
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  desktop_drop:
    :path: Flutter/ephemeral/.symlinks/plugins/desktop_drop/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  file_picker:
    :path: Flutter/ephemeral/.symlinks/plugins/file_picker/macos
  flutter_inappwebview_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos
  flutter_tts:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_tts/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  in_app_purchase_storekit:
    :path: Flutter/ephemeral/.symlinks/plugins/in_app_purchase_storekit/darwin
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  screen_retriever_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin
  sqlite3_flutter_libs:
    :path: Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos

SPEC CHECKSUMS:
  audio_service: aa99a6ba2ae7565996015322b0bb024e1d25c6fd
  audio_session: eaca2512cf2b39212d724f35d11f46180ad3a33e
  audioplayers_darwin: 761f2948df701d05b5db603220c384fb55720012
  battery_plus: f51ad29136e025b714b96f7d096f44f604615da7
  connectivity_plus: 4adf20a405e25b42b9c9f87feff8f4b6fde18a4e
  desktop_drop: e0b672a7d84c0a6cbc378595e82cdb15f2970a43
  device_info_plus: 4fb280989f669696856f8b129e4a5e3cd6c48f76
  file_picker: 7584aae6fa07a041af2b36a2655122d42f578c1a
  flutter_inappwebview_macos: c2d68649f9f8f1831bfcd98d73fd6256366d9d1d
  flutter_tts: ae915565cc6948444b513acc8ee021993281e027
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  in_app_purchase_storekit: d1a48cb0f8b29dbf5f85f782f5dd79b21b90a5e6
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: f0052d280d17aa382b932f399edf32507174e870
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  screen_retriever_macos: 452e51764a9e1cdb74b3c541238795849f21557f
  share_plus: 510bf0af1a42cd602274b4629920c9649c52f4cc
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  sqlite3: fc1400008a9b3525f5914ed715a5d1af0b8f4983
  sqlite3_flutter_libs: f8fc13346870e73fe35ebf6dbb997fbcd156b241
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673
  wakelock_plus: 21ddc249ac4b8d018838dbdabd65c5976c308497
  window_manager: 1d01fa7ac65a6e6f83b965471b1a7fdd3f06166c

PODFILE CHECKSUM: 54d867c82ac51cbd61b565781b9fada492027009

COCOAPODS: 1.16.2
