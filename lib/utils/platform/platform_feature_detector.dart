import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Platform feature detection and capability checking for DassoShu Reader
///
/// Provides runtime detection of platform-specific features and capabilities
/// to ensure graceful degradation and optimal user experience.
class PlatformFeatureDetector {
  static const String _tag = 'PlatformFeatureDetector';

  // Cache for feature detection results
  static final Map<String, bool> _featureCache = {};

  // =====================================================
  // CORE PLATFORM FEATURES
  // =====================================================

  /// Detects if haptic feedback is available
  static Future<bool> hasHapticFeedback() async {
    const key = 'haptic_feedback';
    if (_featureCache.containsKey(key)) {
      return _featureCache[key]!;
    }

    try {
      if (PlatformAdaptations.isIOS) {
        // iOS always has haptic feedback on supported devices
        await HapticFeedback.lightImpact();
        _featureCache[key] = true;
        return true;
      } else if (PlatformAdaptations.isAndroid) {
        // Android haptic feedback availability varies
        await HapticFeedback.vibrate();
        _featureCache[key] = true;
        return true;
      }
    } catch (e) {
      AnxLog.warning('$_tag: Haptic feedback not available: $e');
    }

    _featureCache[key] = false;
    return false;
  }

  /// Detects if biometric authentication is available
  static Future<bool> hasBiometricAuth() async {
    const key = 'biometric_auth';
    if (_featureCache.containsKey(key)) {
      return _featureCache[key]!;
    }

    try {
      // This would typically use local_auth package
      // For now, we'll assume it's available on both platforms
      final available = PlatformAdaptations.isMobile;
      _featureCache[key] = available;
      return available;
    } catch (e) {
      AnxLog.warning('$_tag: Biometric auth detection failed: $e');
      _featureCache[key] = false;
      return false;
    }
  }

  /// Detects if camera is available
  static Future<bool> hasCamera() async {
    const key = 'camera';
    if (_featureCache.containsKey(key)) {
      return _featureCache[key]!;
    }

    try {
      // Both Android and iOS (mobile and tablet) typically have cameras
      final available = PlatformAdaptations.isMobile;
      _featureCache[key] = available;
      return available;
    } catch (e) {
      AnxLog.warning('$_tag: Camera detection failed: $e');
      _featureCache[key] = false;
      return false;
    }
  }

  /// Detects if file picker is available
  static Future<bool> hasFilePicker() async {
    const key = 'file_picker';
    if (_featureCache.containsKey(key)) {
      return _featureCache[key]!;
    }

    try {
      // Both iOS and Android support file picking
      final available = PlatformAdaptations.isMobile;
      _featureCache[key] = available;
      return available;
    } catch (e) {
      AnxLog.warning('$_tag: File picker detection failed: $e');
      _featureCache[key] = false;
      return false;
    }
  }

  // =====================================================
  // EPUB READER SPECIFIC FEATURES
  // =====================================================

  /// Detects if WebView supports JavaScript
  static Future<bool> hasWebViewJavaScript() async {
    const key = 'webview_javascript';
    if (_featureCache.containsKey(key)) {
      return _featureCache[key]!;
    }

    try {
      // Both platforms support JavaScript in WebView
      final available = PlatformAdaptations.isMobile;
      _featureCache[key] = available;
      AnxLog.info('$_tag: WebView JavaScript support: $available');
      return available;
    } catch (e) {
      AnxLog.warning('$_tag: WebView JavaScript detection failed: $e');
      _featureCache[key] = false;
      return false;
    }
  }

  /// Detects if text-to-speech is available
  static Future<bool> hasTextToSpeech() async {
    const key = 'text_to_speech';
    if (_featureCache.containsKey(key)) {
      return _featureCache[key]!;
    }

    try {
      // Both iOS and Android have TTS capabilities
      final available = PlatformAdaptations.isMobile;
      _featureCache[key] = available;
      return available;
    } catch (e) {
      AnxLog.warning('$_tag: TTS detection failed: $e');
      _featureCache[key] = false;
      return false;
    }
  }

  /// Detects if Chinese input methods are available
  static Future<bool> hasChineseInput() async {
    const key = 'chinese_input';
    if (_featureCache.containsKey(key)) {
      return _featureCache[key]!;
    }

    try {
      // Both platforms support Chinese input
      final available = PlatformAdaptations.isMobile;
      _featureCache[key] = available;
      return available;
    } catch (e) {
      AnxLog.warning('$_tag: Chinese input detection failed: $e');
      _featureCache[key] = false;
      return false;
    }
  }

  // =====================================================
  // PLATFORM-SPECIFIC CAPABILITIES
  // =====================================================

  /// Gets platform-specific capabilities
  static Future<PlatformCapabilities> getPlatformCapabilities() async {
    return PlatformCapabilities(
      platform: PlatformAdaptations.platformName,
      hasHapticFeedback: await hasHapticFeedback(),
      hasBiometricAuth: await hasBiometricAuth(),
      hasCamera: await hasCamera(),
      hasFilePicker: await hasFilePicker(),
      hasWebViewJavaScript: await hasWebViewJavaScript(),
      hasTextToSpeech: await hasTextToSpeech(),
      hasChineseInput: await hasChineseInput(),
      supportsBackgroundProcessing: _supportsBackgroundProcessing(),
      supportsNotifications: _supportsNotifications(),
      supportsDeepLinking: _supportsDeepLinking(),
    );
  }

  /// Checks if background processing is supported
  static bool _supportsBackgroundProcessing() {
    if (PlatformAdaptations.isIOS) {
      // iOS has limited background processing
      return true; // Basic background app refresh
    } else if (PlatformAdaptations.isAndroid) {
      // Android has more flexible background processing
      return true;
    }
    return false;
  }

  /// Checks if push notifications are supported
  static bool _supportsNotifications() {
    return PlatformAdaptations.isMobile;
  }

  /// Checks if deep linking is supported
  static bool _supportsDeepLinking() {
    return PlatformAdaptations.isMobile;
  }

  // =====================================================
  // FEATURE COMPATIBILITY MATRIX
  // =====================================================

  /// Gets feature compatibility matrix for debugging
  static Future<Map<String, dynamic>> getCompatibilityMatrix() async {
    final capabilities = await getPlatformCapabilities();

    return {
      'platform': capabilities.platform,
      'core_features': {
        'haptic_feedback': capabilities.hasHapticFeedback,
        'biometric_auth': capabilities.hasBiometricAuth,
        'camera': capabilities.hasCamera,
        'file_picker': capabilities.hasFilePicker,
      },
      'reader_features': {
        'webview_javascript': capabilities.hasWebViewJavaScript,
        'text_to_speech': capabilities.hasTextToSpeech,
        'chinese_input': capabilities.hasChineseInput,
      },
      'system_features': {
        'background_processing': capabilities.supportsBackgroundProcessing,
        'notifications': capabilities.supportsNotifications,
        'deep_linking': capabilities.supportsDeepLinking,
      },
      'platform_specific': _getPlatformSpecificFeatures(),
    };
  }

  /// Gets platform-specific feature information
  static Map<String, dynamic> _getPlatformSpecificFeatures() {
    if (PlatformAdaptations.isIOS) {
      return {
        'cupertino_widgets': true,
        'ios_navigation': true,
        'app_store_integration': true,
        'ios_sharing': true,
      };
    } else if (PlatformAdaptations.isAndroid) {
      return {
        'material_widgets': true,
        'android_navigation': true,
        'play_store_integration': true,
        'android_sharing': true,
        'external_storage': true,
      };
    }
    return {};
  }

  /// Clears the feature detection cache
  static void clearCache() {
    _featureCache.clear();
    AnxLog.info('$_tag: Feature detection cache cleared');
  }

  /// Logs all detected capabilities
  static Future<void> logCapabilities() async {
    final capabilities = await getPlatformCapabilities();
    final matrix = await getCompatibilityMatrix();

    AnxLog.info('$_tag: Platform Capabilities for ${capabilities.platform}:');
    AnxLog.info('$_tag: Haptic Feedback: ${capabilities.hasHapticFeedback}');
    AnxLog.info('$_tag: Biometric Auth: ${capabilities.hasBiometricAuth}');
    AnxLog.info('$_tag: Camera: ${capabilities.hasCamera}');
    AnxLog.info('$_tag: File Picker: ${capabilities.hasFilePicker}');
    AnxLog.info(
      '$_tag: WebView JavaScript: ${capabilities.hasWebViewJavaScript}',
    );
    AnxLog.info('$_tag: Text-to-Speech: ${capabilities.hasTextToSpeech}');
    AnxLog.info('$_tag: Chinese Input: ${capabilities.hasChineseInput}');
    AnxLog.info(
      '$_tag: Background Processing: ${capabilities.supportsBackgroundProcessing}',
    );
    AnxLog.info('$_tag: Notifications: ${capabilities.supportsNotifications}');
    AnxLog.info('$_tag: Deep Linking: ${capabilities.supportsDeepLinking}');

    if (kDebugMode) {
      AnxLog.info('$_tag: Full compatibility matrix: $matrix');
    }
  }
}

/// Represents platform capabilities
class PlatformCapabilities {
  final String platform;
  final bool hasHapticFeedback;
  final bool hasBiometricAuth;
  final bool hasCamera;
  final bool hasFilePicker;
  final bool hasWebViewJavaScript;
  final bool hasTextToSpeech;
  final bool hasChineseInput;
  final bool supportsBackgroundProcessing;
  final bool supportsNotifications;
  final bool supportsDeepLinking;

  const PlatformCapabilities({
    required this.platform,
    required this.hasHapticFeedback,
    required this.hasBiometricAuth,
    required this.hasCamera,
    required this.hasFilePicker,
    required this.hasWebViewJavaScript,
    required this.hasTextToSpeech,
    required this.hasChineseInput,
    required this.supportsBackgroundProcessing,
    required this.supportsNotifications,
    required this.supportsDeepLinking,
  });

  /// Returns true if all critical features are available
  bool get hasAllCriticalFeatures {
    return hasWebViewJavaScript && hasFilePicker && hasChineseInput;
  }

  /// Returns a summary of missing features
  List<String> get missingFeatures {
    final missing = <String>[];

    if (!hasHapticFeedback) missing.add('Haptic Feedback');
    if (!hasBiometricAuth) missing.add('Biometric Authentication');
    if (!hasCamera) missing.add('Camera');
    if (!hasFilePicker) missing.add('File Picker');
    if (!hasWebViewJavaScript) missing.add('WebView JavaScript');
    if (!hasTextToSpeech) missing.add('Text-to-Speech');
    if (!hasChineseInput) missing.add('Chinese Input');

    return missing;
  }
}
